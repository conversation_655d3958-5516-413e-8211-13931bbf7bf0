// Note: agentq_mobile_automation_test is designed for mobile apps, not web browsers
import { describe, it } from 'mocha';
import { Storage } from '@google-cloud/storage';
import * as path from 'path';
import * as fs from 'fs';
import * as dotenv from 'dotenv';
import { q, <PERSON><PERSON>erContext } from 'agentq_mobile_automation_test';
import { CapabilityFactory } from '../src/services/capability-factory';

// Load environment variables
dotenv.config();

// Initialize Google Cloud Storage
const storage = new Storage({
  projectId: process.env.GCP_PROJECT_ID,
  credentials: {
    client_email: process.env.GCP_CLIENT_EMAIL,
    private_key: process.env.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  },
});

// Function to download file from Google Cloud Storage
async function downloadFileFromGCS(gsUrl: string): Promise<string> {
  try {
    // Parse the gs:// URL
    const urlParts = gsUrl.replace('gs://', '').split('/');
    const bucketName = urlParts[0];
    const fileName = urlParts.slice(1).join('/');

    // console.log(`📥 Downloading file from GCS: ${bucketName}/${fileName}`);

    // Create local downloads directory if it doesn't exist
    const downloadsDir = path.join(process.cwd(), 'downloads');
    if (!fs.existsSync(downloadsDir)) {
      fs.mkdirSync(downloadsDir, { recursive: true });
    }

    // Generate local file path
    const localFileName = path.basename(fileName);
    const localFilePath = path.join(downloadsDir, localFileName);

    // Download the file
    const bucket = storage.bucket(bucketName);
    const file = bucket.file(fileName);

    await file.download({ destination: localFilePath });

    // console.log(`✅ File downloaded successfully to: ${localFilePath}`);
    return localFilePath;

  } catch (error) {
    console.error(`❌ Failed to download file from GCS: ${error}`);
    throw error;
  }
}

// Load test data from environment
const testDataEnv = process.env.TEST_DATA;
const clientId = process.env.CLIENT_ID;
const testCaseId = process.env.TEST_CASE_ID;
let stepsData: any = null;

if (testDataEnv) {
  try {
    stepsData = JSON.parse(testDataEnv);
    console.log(`📱 Loaded test data with ${stepsData.steps?.length || 0} steps`);
  } catch (error) {
    console.error('Failed to parse test data:', error);
  }
}

// Use the CapabilityFactory for dynamic capability generation

// Use unique identifier instead of test title to prevent data mixing
const uniqueTestName = clientId ? `Test-${clientId}` : (testCaseId ? `TestCase-${testCaseId}` : 'Dynamic Test Case');

// Mobile-only testing approach - skip web browser testing
if (stepsData && stepsData.steps) {
  const dynamicCapabilities = CapabilityFactory.createCapabilities(stepsData.steps);

  if (dynamicCapabilities && CapabilityFactory.isMobilePlatform(dynamicCapabilities)) {
    // console.log('🔧 Detected mobile capabilities:', JSON.stringify(dynamicCapabilities, null, 2));
    // console.log('📱 Running mobile test with agentq_mobile_automation_test');
    // console.log('🔗 Connecting to Appium server at http://localhost:4723');

    // Create proper Mocha test structure
    describe(uniqueTestName, () => {
      it('should execute mobile test steps', async function() {
        this.timeout(300000); // 5 minutes timeout for mobile tests

        // console.log('🚀 Starting mobile test execution');

        // Use existing browser session from wdio.conf.ts (like successful simplified version)
        try {
          // console.log('� Using existing browser session from wdio.conf.ts');

          // Use the global browser object instead of creating a new session
          if (typeof browser === 'undefined') {
            throw new Error('Browser session not available - make sure this runs within WebDriverIO context');
          }

          // console.log('✅ Using existing WebDriverIO session');
          console.log(`🔍 Session ID: ${browser.sessionId}`);
          // console.log(`🔍 Browser object available: ${!!browser}`);
          // console.log(`🔍 Browser type: ${typeof browser}`);
          // console.log(`🔍 Steps to execute: ${stepsData.steps.length}`);

          // Set browser context for q() function to work
          // console.log('🔧 Setting browser context for q() function...');
          BrowserContext.getInstance().setBrowser(browser);
          // console.log('✅ Browser context set successfully for q() function');

          // Add a small delay to ensure app is fully loaded
          // console.log('⏱️ Waiting 5 seconds for app to fully load...');
          await browser.pause(5000);

          await executeTestSteps(stepsData.steps, browser);
          console.log('✅ All test steps completed successfully');

          // Explicit Device Farm cleanup
          try {
            console.log('🧹 Cleaning up Device Farm session...');
            // Use a timeout to prevent hanging on Device Farm commands
            await Promise.race([
              browser.executeScript('devicefarm: stopVideoRecording', [{}]),
              new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
            ]);
            console.log('✅ Device Farm video recording stopped');
          } catch (error) {
            console.log(`⚠️ Device Farm video stop failed (may already be stopped): ${error}`);
          }

        } catch (error) {
          console.error('❌ Mobile test execution failed:', error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes('timeout')) {
            console.error('💡 Device farm connection timeout - check if Appium server is running at localhost:4723');
          }
          throw error;
        }
      });
    });
  } else {
    console.log('🚫 No valid mobile capabilities found - test skipped');
    console.log('💡 This service only supports mobile app testing (iOS/Android)');
    console.log('📋 Required: setup action with platform, deviceName, deviceId, and app file');

    // Create a failing test to indicate the issue
    describe(uniqueTestName, () => {
      it('should require mobile setup action', async () => {
        throw new Error('Mobile setup action required. This service only supports iOS and Android app testing.');
      });
    });
  }
} else {
  // No test data provided
  describe(uniqueTestName, () => {
    it('should require test data', async () => {
      console.log('⚠️ No test data provided');
      throw new Error('Test data with mobile setup action is required for mobile app testing.');
    });
  });
}

// Function to execute test steps (shared between mobile and web)
async function executeTestSteps(steps: any[], browser: any) {
  try {
    console.log(`📋 ===== STARTING TEST STEP EXECUTION =====`);

    // Filter out setup steps (like successful simplified version)
    const testSteps = steps.filter(step => {
      let actions = [];
      if (step.Actions) {
        try {
          actions = JSON.parse(step.Actions);
        } catch (error) {
          return true; // Include if can't parse
        }
      } else if (step.action) {
        actions = [step];
      }
      return !actions.some((action: any) => action.action === 'setup');
    });
    for (const step of testSteps) {
      let actions = [];

      if (step.Actions) {
        // Frontend format - parse JSON string
        try {
          actions = JSON.parse(step.Actions);
        } catch (error) {
          console.error(`Failed to parse actions for step ${step.step}:`, error);
          continue;
        }
      } else if (step.action) {
        // Transformed format - use direct properties
        actions = [{
          action: step.action,
          target: step.target,
          value: step.value,
          fileUrl: step.fileUrl,
          fileId: step.fileId,
          deviceId: step.deviceId,
          deviceName: step.deviceName,
          platform: step.platform,
          version: step.version
        }];
      } else {
        console.log(`Step ${step.step}: No actions found - skipping`);
        continue;
      }

      // Process each action in the step
      for (const action of actions) {
        if (action.action === 'setup') {
          console.log(`Step ${step.step}: setup - Configuration already applied ✓`);
          continue;
        }

        if (action.action === 'prompt' && action.value) {
          // console.log(`\n🔥 ===== EXECUTING STEP ${step.step}: PROMPT ACTION =====`);
          const promptText = action.value || action.prompt || action.target;
          // console.log(`💬 Processing prompt with q() function: "${promptText}"`);

          try {
            // Use the q() function to send prompt to LLM and execute the generated command
            await q(promptText);
            // console.log(`✅ Successfully executed prompt via q() function: "${promptText}"`);
          } catch (error) {
            // console.error(`❌ Failed to execute prompt action via q(): "${promptText}"`, error);
            throw error;
          }
        } else if (action.action === 'Go to Page' && action.target) {
          console.log(`Step ${step.step}: goto ${action.target} ✓`);
          await browser.url(action.target);
        } else if (action.action === 'goto' && action.target) {
          console.log(`Step ${step.step}: goto ${action.target} ✓`);
          await browser.url(action.target);
        } else if (action.action === 'navigate' && action.target) {
          console.log(`Step ${step.step}: navigate ${action.target} ✓`);
          await browser.url(action.target);
        } else if (action.action === 'Fill' && action.target && action.value) {
          console.log(`Step ${step.step}: fill ${action.target} ${action.value} ✓`);
          const element = browser.$(action.target);
          await element.setValue(action.value);
        } else if (action.action === 'write' && action.target && action.value) {
          // console.log(`\n🔥 ===== EXECUTING STEP ${step.step}: WRITE ACTION =====`);
          console.log(`⌨️ Writing "${action.value}" to element "${action.target}"`);
          console.log(`🔍 Action details:`, JSON.stringify(action, null, 2));
          try {
            console.log(`🔍 Getting element with selector: ${action.target}`);
            // Use accessibility ID selector (like working sample)
            const element = browser.$(`~${action.target.replace('~', '')}`);
            console.log(`🔍 Element object created`);

            console.log(`⏳ Waiting for element to exist...`);
            await element.waitForExist({ timeout: 10000 });
            console.log(`✅ Element found, setting value...`);

            await element.setValue(action.value);
            console.log(`✅ Successfully wrote "${action.value}" to "${action.target}"`);
          } catch (error) {
            console.error(`❌ Failed to write to element "${action.target}":`, error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error(`❌ Error details:`, errorMessage);
            throw error;
          }
        } else if (action.action === 'Click' && action.target) {
          console.log(`Step ${step.step}: click ${action.target} ✓`);
          const element = browser.$(action.target);
          await element.click();
        } else if (action.action === 'click' && action.target) {
          // console.log(`\n🔥 ===== EXECUTING STEP ${step.step}: CLICK ACTION =====`);
          console.log(`🖱️ Clicking element "${action.target}"`);
          try {
            console.log(`🔍 Getting element with selector: ${action.target}`);
            // Use accessibility ID selector (like working sample)
            const element = browser.$(`~${action.target.replace('~', '')}`);

            console.log(`⏳ Waiting for element to exist...`);
            await element.waitForExist({ timeout: 10000 });
            console.log(`✅ Element found, clicking...`);

            await element.click();
            console.log(`✅ Successfully clicked "${action.target}"`);
          } catch (error) {
            console.error(`❌ Failed to click element "${action.target}":`, error);
            throw error;
          }
        } else if (action.action === 'Pause' && action.value) {
          console.log(`Step ${step.step}: pause ${action.value} ✓`);
          await browser.pause(parseInt(action.value) * 1000);
        } else if (action.action === 'pause' && action.value) {
          console.log(`Step ${step.step}: pause ${action.value} ✓`);
          await browser.pause(parseInt(action.value) * 1000);
        } else if (action.action === 'Upload' && action.target && action.value) {
          console.log(`Step ${step.step}: upload ${action.target} ${action.value} ✓`);
          let filePath = action.fileUrl;

          // If fileUrl is a Google Cloud Storage URL, download it first
          if (action.fileUrl && action.fileUrl.startsWith('gs://')) {
            filePath = await downloadFileFromGCS(action.fileUrl);
          }

          const element = browser.$(action.target);
          await element.addValue(filePath);

        } else if (action.action === 'upload' && action.target && action.value) {
          console.log(`Step ${step.step}: upload ${action.target} ${action.value} ✓`);
          let filePath = action.fileUrl;

          // If fileUrl is a Google Cloud Storage URL, download it first
          if (action.fileUrl && action.fileUrl.startsWith('gs://')) {
            filePath = await downloadFileFromGCS(action.fileUrl);
          }

          const element = browser.$(action.target);
          await element.addValue(filePath);
        } else if (action.action === 'assertText' && action.target && action.value) {
          console.log(`Step ${step.step}: assertText ${action.target} ${action.value} ✓`);
          const element = browser.$(action.target);
          const text = await element.getText();
          if (text !== action.value) {
            throw new Error(`Expected text '${action.value}', but got '${text}'`);
          }
        } else if (action.action === 'assertUrl' && action.value) {
          console.log(`Step ${step.step}: assertUrl ${action.value} ✓`);
          const currentUrl = await browser.getUrl();
          if (currentUrl !== action.value) {
            throw new Error(`Expected URL '${action.value}', but got '${currentUrl}'`);
          }
        } else {
          console.log(`Step ${step.step}: ${action.action} - Skipped (unsupported action)`);
        }

        // Small delay between actions (like successful simplified version)
        await browser.pause(1000);
      }
    }

    // console.log('\n🎉 All test steps completed successfully!');

    // Additional verification - try to get current activity (like simplified version)
    try {
      const currentActivity = await browser.getCurrentActivity();
      // console.log(`📱 Current activity after test: ${currentActivity}`);
    } catch (error) {
      console.log('⚠️ Could not get current activity:', error);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}
