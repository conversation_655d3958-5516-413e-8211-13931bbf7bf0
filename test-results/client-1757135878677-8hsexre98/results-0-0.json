{"start": "2025-09-06T05:18:02.695Z", "end": "2025-09-06T05:18:02.696Z", "capabilities": {"platformName": "Android", "appium:platformVersion": "16", "appium:deviceName": "Medium_Phone_API_36.0", "appium:automationName": "UiAutomator2", "appium:app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "appium:newCommandTimeout": 300, "appium:sessionOverride": true, "appium:autoGrantPermissions": true, "appium:noReset": false, "appium:fullReset": false, "appium:appWaitActivity": "*", "appium:appWaitDuration": 15000, "appium:androidInstallTimeout": 120000, "appium:uiautomator2ServerInstallTimeout": 60000, "appium:uiautomator2ServerLaunchTimeout": 60000, "appium:skipServerInstallation": false, "appium:skipDeviceInitialization": false, "appium:disableWindowAnimation": false, "appium:skipUnlock": true, "appium:androidDeviceReadyTimeout": 60, "appium:appWaitForLaunch": true, "df:accesskey": "admin_AzZvGSzPbIrrEx", "df:token": "9de28a88-21b5-4887-8282-848106f3afe5", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757135880953", "df:videoTimeLimit": null, "df:videoStartDelay": 0, "df:recordFromSessionStart": true, "df:options": {"saveDeviceLogs": true, "build": "test_1757135880953", "videoStartDelay": 0, "recordFromSessionStart": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "df:autoMarkTestStatus": true, "df:testStatusFromLogs": true, "df:testStatusFromExitCode": true, "df:forceTestStatusUpdate": true, "appium:udid": "emulator-5554", "hostname": "localhost", "port": 4723, "path": "/wd/hub"}, "framework": "mocha", "mochaOpts": {"timeout": 120000, "ui": "bdd"}, "suites": [], "specs": ["file:///Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/tests/master.spec.ts"], "state": {"passed": 0, "failed": 0, "skipped": 0}}