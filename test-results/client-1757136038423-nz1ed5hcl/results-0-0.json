{"start": "2025-09-06T05:20:58.402Z", "end": "2025-09-06T05:21:13.349Z", "capabilities": {"platformName": "Android", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757136040776", "df:videoTimeLimit": null, "df:videoStartDelay": 0, "df:recordFromSessionStart": true, "df:options": {"saveDeviceLogs": true, "build": "test_1757136040776", "videoStartDelay": 0, "recordFromSessionStart": true, "accesskey": "agentq_4dZFoEnNB2IvX", "token": "39a3d273-bdf4-4e2b-b443-42c5fd805a0a", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757136040776", "videoTimeLimit": null, "options": {"saveDeviceLogs": true, "build": "test_1757136040776", "videoStartDelay": 0, "recordFromSessionStart": true, "accesskey": "agentq_4dZFoEnNB2IvX", "token": "39a3d273-bdf4-4e2b-b443-42c5fd805a0a", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757136040776", "videoTimeLimit": null}, "enableTestStatusReporting": true, "testResultsEndpoint": "/test-results", "logTestResults": true, "markTestStatus": true, "autoMarkTestStatus": true, "testStatusFromLogs": true, "testStatusFromExitCode": true, "forceTestStatusUpdate": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "df:autoMarkTestStatus": true, "df:testStatusFromLogs": true, "df:testStatusFromExitCode": true, "df:forceTestStatusUpdate": true, "platformVersion": "16", "deviceName": "emulator-5554", "automationName": "UiAutomator2", "newCommandTimeout": 300, "sessionOverride": true, "autoGrantPermissions": true, "noReset": false, "fullReset": false, "appWaitActivity": "*", "appWaitDuration": 15000, "androidInstallTimeout": 120000, "uiautomator2ServerInstallTimeout": 60000, "uiautomator2ServerLaunchTimeout": 60000, "skipServerInstallation": false, "skipDeviceInitialization": false, "disableWindowAnimation": false, "skipUnlock": true, "androidDeviceReadyTimeout": 60, "appWaitForLaunch": true, "app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "udid": "emulator-5554", "systemPort": 58191, "chromeDriverPort": 58192, "adbPort": 5037, "mjpegServerPort": 58193, "platform": "LINUX", "webStorageEnabled": false, "takesScreenshot": true, "javascriptEnabled": true, "databaseEnabled": false, "networkConnectionEnabled": true, "locationContextEnabled": false, "warnings": {}, "desired": {"platformName": "Android", "df:recordVideo": true, "df:videoQuality": "high", "df:videoFPS": 15, "df:recordVideoOnFailureOnly": false, "df:videoName": "test_1757136040776", "df:videoTimeLimit": null, "df:videoStartDelay": 0, "df:recordFromSessionStart": true, "df:options": {"saveDeviceLogs": true, "build": "test_1757136040776", "videoStartDelay": 0, "recordFromSessionStart": true, "accesskey": "agentq_4dZFoEnNB2IvX", "token": "39a3d273-bdf4-4e2b-b443-42c5fd805a0a", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757136040776", "videoTimeLimit": null, "options": {"saveDeviceLogs": true, "build": "test_1757136040776", "videoStartDelay": 0, "recordFromSessionStart": true, "accesskey": "agentq_4dZFoEnNB2IvX", "token": "39a3d273-bdf4-4e2b-b443-42c5fd805a0a", "recordVideo": true, "videoQuality": "high", "videoFPS": 15, "recordVideoOnFailureOnly": false, "videoName": "test_1757136040776", "videoTimeLimit": null}, "enableTestStatusReporting": true, "testResultsEndpoint": "/test-results", "logTestResults": true, "markTestStatus": true, "autoMarkTestStatus": true, "testStatusFromLogs": true, "testStatusFromExitCode": true, "forceTestStatusUpdate": true}, "df:enableTestStatusReporting": true, "df:testResultsEndpoint": "/test-results", "df:logTestResults": true, "df:markTestStatus": true, "df:autoMarkTestStatus": true, "df:testStatusFromLogs": true, "df:testStatusFromExitCode": true, "df:forceTestStatusUpdate": true, "platformVersion": "16", "deviceName": "Medium_Phone_API_36.0", "automationName": "UiAutomator2", "newCommandTimeout": 300, "sessionOverride": true, "autoGrantPermissions": true, "noReset": false, "fullReset": false, "appWaitActivity": "*", "appWaitDuration": 15000, "androidInstallTimeout": 120000, "uiautomator2ServerInstallTimeout": 60000, "uiautomator2ServerLaunchTimeout": 60000, "skipServerInstallation": false, "skipDeviceInitialization": false, "disableWindowAnimation": false, "skipUnlock": true, "androidDeviceReadyTimeout": 60, "appWaitForLaunch": true, "app": "/Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/client-apps/session-41d09021-1a6c-4a13-8a1a-edee6554ba97/1756047271343-Android.SauceLabs.Mobile.Sample.app.2.7.1.apk", "udid": "emulator-5554", "systemPort": 58191, "chromeDriverPort": 58192, "adbPort": 5037, "mjpegServerPort": 58193}, "deviceUDID": "emulator-5554", "appPackage": "com.swaglabsmobileapp", "appActivity": "com.swaglabsmobileapp.SplashActivity", "pixelRatio": "2.625", "statBarHeight": 63, "viewportRect": {"left": 0, "top": 63, "width": 1080, "height": 2337}, "deviceApiLevel": 36, "deviceManufacturer": "Google", "deviceModel": "sdk_gphone64_arm64", "deviceScreenSize": "1080x2400", "deviceScreenDensity": 420, "sessionId": "3973b464-274d-4fb3-9742-0f27388cb7fe"}, "framework": "mocha", "mochaOpts": {"timeout": 120000, "ui": "bdd"}, "suites": [{"name": "Test-client-1757136038423-nz1ed5hcl", "duration": 13515, "start": "2025-09-06T05:20:58.404Z", "end": "2025-09-06T05:21:11.919Z", "sessionId": "3973b464-274d-4fb3-9742-0f27388cb7fe", "tests": [{"name": "should execute mobile test steps", "start": "2025-09-06T05:20:58.404Z", "end": "2025-09-06T05:21:11.911Z", "duration": 13507, "state": "passed"}], "hooks": []}], "specs": ["file:///Users/<USER>/Documents/github/AGENTQ-AI/websocket_ai_mobile_single_test/tests/master.spec.ts"], "state": {"passed": 1, "failed": 0, "skipped": 0}}